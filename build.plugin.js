/* eslint-disable import/no-extraneous-dependencies */
/* eslint-disable import/no-unresolved */
/* eslint-disable @typescript-eslint/no-require-imports */
/**
 * https://github.com/ice-lab/build-scripts
 */
const CopyPlugin = require('copy-webpack-plugin');
const CompressionWebpackPlugin = require('compression-webpack-plugin');
// const BundleAnalyzerPlugin = require("webpack-bundle-analyzer").BundleAnalyzerPlugin
const WebpackAssetsHashPlugin = require('@yth/webpack-assets-hash-plugin');
const webpack = require('webpack');
const TerserPlugin = require('terser-webpack-plugin');
const path = require('path');
require('dotenv').config({
  path: `.env.${process.env.PLATFORM}`,
});

module.exports = ({ onGetWebpackConfig }) => {
  onGetWebpackConfig((config) => {
    // 动态设置 publicPath，支持 icestark 注入路径
    config.output.publicPath('/');

    // ✅ 设置别名（可选）
    config.resolve.alias.set('@', path.resolve(__dirname, 'src'));

    config
      .plugin('CopyWebpackPlugin')
      .use(new CopyPlugin([{ from: `${__dirname}/src/assets`, to: 'assets' }]));

    // 资源压缩
    config.plugin('CompressionWebpackPlugin').use(
      new CompressionWebpackPlugin({
        algorithm: 'gzip',
        test: /\.(js|css)$/,
        threshold: 500,
        minRatio: 0.8,
      }),
    );

    // 资源版本号生成
    config.plugin('WebpackAssetsHashPlugin').use(
      new WebpackAssetsHashPlugin({
        // 是否删除hash前原始文件,暂时保留原文件做兼容,稳定运行后可删除
        removeOriginalFile: false,
      }),
    );

    // 增加配置去除console和debugger
    config.optimization.minimizer('terser').use(TerserPlugin, [
      {
        terserOptions: {
          compress: {
            drop_console: true, // 删除console
            drop_debugger: true, // 删除debugger
          },
        },
      },
    ]);

    // 用于分析打包打包结果
    // config.plugin("BundleAnalyzerPlugin").use(
    //  new BundleAnalyzerPlugin({
    //      analyzerPort: 0,
    //  })
    // )

    const env = Object.keys(process.env).reduce((acc, envKey) => {
      if (envKey.startsWith('YTH')) {
        acc[envKey] = JSON.stringify(process.env[envKey]);
      }
      return acc;
    }, {});

    config.plugin('DefinePlugin').use(
      new webpack.DefinePlugin({
        process: {
          env,
        },
      }),
    );
  });
};
