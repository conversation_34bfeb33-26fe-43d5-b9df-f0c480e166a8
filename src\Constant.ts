import { isEmpty, YTHLocalization } from 'yth-ui';

export const LANGUAGE_STORE_KEY: string = '$_language';

export const CurrentUser: () => Record<string, string> = () => {
  // return JSON.parse(window.sessionStorage.getItem('$_user') || '{}');
  return {
    id: 'admin',
    username: 'admin',
    realName: '超级管理员',
    accountId: '********',
    unitId: '********',
    tenantId: 1,
    unitCode: '********',
    unitName: '中华人民共和国',
    unitType: '-2',
  };
};

export const Token: () => string = () => {
  // return window.sessionStorage.getItem('$_token') || '';
  return 'bearer af258bd5-0531-4857-af61-809a5e286b58';
};

export const Setting: () => Record<string, string> = () => {
  return JSON.parse(window.localStorage.getItem('yth_form_config_setting') || '{}');
};

/**
 * 配置request请求时的默认参数
 */
export const ConstHeaders: () => Headers = () => {
  const { affinityHost = '' } = Setting();
  const headers: Headers = new Headers();
  headers.append('Content-Language', YTHLocalization.getLanguage());
  if (Token()) {
    headers.append('Authorization', Token() || '');
  }
  if (!isEmpty(affinityHost)) {
    headers.append('affinity_host', affinityHost);
  }
  return headers;
};
