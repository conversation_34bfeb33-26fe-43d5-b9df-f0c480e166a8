import React, { useState, useEffect } from 'react';
import { YTHForm } from 'yth-ui';
import planApi from '@/service/inspection/planApi';
import { message, But<PERSON>, Spin, Input } from 'antd';
import { Token } from '@/Constant';
import style from '@/pages/inspection/common/css/inspection.module.less';
import formApi from '@/service/formApi';
import dicParams from '@/pages/inspection/common/util/dicParams';

import { validateMobileOrLinePhone, validateTransportLicense } from '@/utils/customMethod';
import { BaseResponse } from '@/types/common';
import { InspectionPlanVo, PlanInsertParam } from '@/types/inspection/plan';

/**
 * @description 弹窗参数类型定义
 */
type PropsTypes = {
  /** 弹窗的类别 add 新增 view 查看 edit 编辑 */
  type: string;
  /** 弹窗传入的数据 */
  dataObj: { id?: string; [key: string]: React.Key };
  /** 关闭弹窗的回调函数 */
  closeModal: () => void;
};

/**
 * @description 查看 或新增 modal
 * @param PropsTypes PropsTypes
 */
const AddDialog: React.FC<PropsTypes> = ({ type, dataObj, closeModal = () => {} }) => {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isCasRequested, setIsCasRequested] = useState<boolean>(false);

  // 表单
  const form: ReturnType<typeof YTHForm.createForm> = React.useMemo(
    () => YTHForm.createForm({}),
    [],
  );

  const { TextArea } = Input;

  // 查询详情
  const queryDataDetail: () => Promise<void> = async () => {
    setIsLoading(true);
    const res: BaseResponse<InspectionPlanVo> = await planApi.getDetailById({
      id: dataObj.id,
    });
    if (res && res.code && res.code === 200) {
      const formD: InspectionPlanVo = res.data;
      // if (formD.companyType && dataObj.companyTypeText) {
      //   formD.companyTypeData = [
      //     {
      //       code: String(dataObj.companyType ?? ''),
      //       text: String(dataObj.companyTypeText ?? ''),
      //     },
      //   ];
      // }
      form.setValues(formD);
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (type && (type === 'edit' || type === 'view')) {
      queryDataDetail().then(() => {
        setIsCasRequested(true);
      });
    } else {
      setIsCasRequested(true);
    }
  }, [type, dataObj]);

  // 点击取消
  const cancel: () => void = () => {
    form.reset();
    closeModal();
  };

  // 新增保存
  const submitAddData: (data: PlanInsertParam) => Promise<void> = async (data) => {
    setIsLoading(true);
    const res: BaseResponse<object> = await planApi.insert(data);
    if (res && res.code && res.code === 200) {
      message.success('新增数据成功');
      closeModal();
    } else {
      message.error('新增数据失败');
    }
    setIsLoading(false);
  };

  // 编辑保存
  const submitEditData: (data: PlanUpdateParam) => Promise<void> = async (data) => {
    setIsLoading(true);
    const res: BaseResponse<object> = await planApi.update({ ...data, id: dataObj?.id });
    if (res && res.code && res.code === 200) {
      message.success('更新数据成功');
      closeModal();
    } else {
      message.error('更新数据失败');
    }
    setIsLoading(false);
  };

  // 点击保存
  const save: () => Promise<void> = async () => {
    form.validate().then(() => {
      const submitData: PlanInsertParam = JSON.parse(JSON.stringify(form.values));
      if (type === 'add') {
        submitAddData(submitData);
      } else if (type === 'edit') {
        submitEditData(submitData);
      }
    });
  };

  // 删除数据dialog

  return (
    <div className={style['yth-inspection-moduel']}>
      <Spin spinning={isLoading}>
        {isCasRequested && (
          <YTHForm form={form} col={2}>
            <YTHForm.Item
              name="id"
              title="id"
              labelType={2}
              required={false}
              display="hidden"
              componentName="Input"
              componentProps={{
                disabled: true,
              }}
            />

            <YTHForm.Item
              name="companyName"
              title="企业名称"
              labelType={2}
              required
              componentName="Input"
              componentProps={{
                disabled: type === 'view',
              }}
            />
            <YTHForm.Item
              name="companyTypeData"
              title="企业类型"
              labelType={2}
              required
              componentName="Selector"
              componentProps={{
                request: async () => {
                  return formApi.getDictData(dicParams.companyType);
                },
                disabled: type === 'view',
                p_props: {
                  placeholder: '请输入',
                },
              }}
            />

            <YTHForm.Item
              name="licenseNumber"
              title="道路运输许可证"
              labelType={2}
              validator={validateTransportLicense}
              required
              componentName="Input"
              componentProps={{
                disabled: type === 'view',
              }}
            />
            <YTHForm.Item
              required
              labelType={2}
              title="运输许可证到期时间"
              name="licenseExpiryDate"
              componentName="DatePicker"
              componentProps={{
                placeholder: '',
                precision: 'day',
                formatter: 'YYYY-MM-DD',
                disabled: type === 'view',
              }}
            />
            <YTHForm.Item
              name="contactPerson"
              title="联系人"
              labelType={2}
              required
              componentName="Input"
              componentProps={{
                disabled: type === 'view',
              }}
            />
            <YTHForm.Item
              name="phoneNumber"
              title="联系电话"
              labelType={2}
              validator={validateMobileOrLinePhone}
              required
              componentName="Input"
              componentProps={{
                disabled: type === 'view',
              }}
            />
            <YTHForm.Item
              name="address"
              title="联系地址"
              labelType={2}
              required
              componentName="Input"
              componentProps={{
                disabled: type === 'view',
              }}
              mergeRow={2}
            />
            <YTHForm.Item
              name="businessScope"
              title="经营范围"
              labelType={2}
              required
              component={TextArea}
              componentProps={{
                disabled: type === 'view',
              }}
              mergeRow={2}
            />
            <YTHForm.Item
              name="licenseAttachments"
              title="附件"
              labelType={2}
              required={false}
              componentName="Upload"
              componentProps={{
                listType: `yth-card`,
                name: 'file',
                action: '/gw/form-api/file/upload',
                headers: {
                  authorization: Token(),
                },
                online: '/preview/onlinePreview',
                data: {
                  formCode: 'campus_gallery',
                },
                disabled: type === 'view',
              }}
            />
          </YTHForm>
        )}
        <div className={style['drawer-filter-operation']}>
          {(type === 'add' || type === 'edit') && (
            <Button onClick={save} className={style['search-btn']} type="primary">
              保存
            </Button>
          )}
          <Button onClick={cancel} className={style['reset-btn']}>
            取消
          </Button>
        </div>
      </Spin>
    </div>
  );
};
export default AddDialog;
